var createError = require('http-errors');
var express = require('express');
var path = require('path');
var cookieParser = require('cookie-parser');
var logger = require('morgan');
var helmet = require('helmet');
var rateLimit = require('express-rate-limit');
var cors = require('cors');
var compression = require('compression');

var indexRouter = require('./routes/index');
var usersRouter = require('./routes/users');

var app = express();

// 安全配置 - 禁用 X-Powered-By 头
app.disable('x-powered-by');

// 安全中间件配置
// 使用 Helmet 设置安全 HTTP 头
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// 启用 CORS（根据需要配置）
app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? false : true, // 生产环境中应指定具体域名
  credentials: true
}));

// 启用 gzip 压缩
app.use(compression());

// 速率限制 - 防止暴力攻击
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 100, // 限制每个 IP 15 分钟内最多 100 个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// 更严格的登录端点速率限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 5, // 限制每个 IP 15 分钟内最多 5 次登录尝试
  message: {
    error: '登录尝试次数过多，请 15 分钟后再试'
  },
  skipSuccessfulRequests: true,
});

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'pug');

app.use(logger('dev'));
// 限制请求体大小
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));
// 安全的 cookie 配置
app.use(cookieParser(process.env.COOKIE_SECRET || 'your-secret-key', {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict'
}));
app.use(express.static(path.join(__dirname, 'public')));

app.use('/', indexRouter);
app.use('/users', usersRouter);

// 为登录相关路由应用更严格的速率限制
app.use('/login', loginLimiter);
app.use('/auth', loginLimiter);

// catch 404 and forward to error handler
app.use(function(req, res, next) {
  const err = createError(404, '页面未找到');
  next(err);
});

// 安全的错误处理器
app.use(function(err, req, res, next) {
  // 记录错误（在生产环境中应使用专业的日志系统）
  if (process.env.NODE_ENV !== 'test') {
    console.error('Error:', {
      message: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  }

  // 设置本地变量，仅在开发环境中提供错误详情
  res.locals.message = err.message;
  res.locals.error = process.env.NODE_ENV === 'development' ? err : {};

  // 在生产环境中不暴露敏感错误信息
  if (process.env.NODE_ENV === 'production') {
    if (err.status === 404) {
      res.locals.message = '页面未找到';
    } else if (err.status >= 400 && err.status < 500) {
      res.locals.message = '请求错误';
    } else {
      res.locals.message = '服务器内部错误';
    }
  }

  // 渲染错误页面
  res.status(err.status || 500);
  res.render('error');
});

module.exports = app;
