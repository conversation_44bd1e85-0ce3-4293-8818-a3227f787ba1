const { body, param, query, validationResult } = require('express-validator');

/**
 * 处理验证错误的中间件
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: '输入验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * 常用的验证规则
 */
const validationRules = {
  // 邮箱验证
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),

  // 密码验证
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('密码至少需要8个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),

  // 用户名验证
  username: body('username')
    .isLength({ min: 3, max: 20 })
    .withMessage('用户名长度必须在3-20个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),

  // ID 参数验证
  id: param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数'),

  // 防止开放重定向的 URL 验证
  redirectUrl: query('redirect')
    .optional()
    .custom((value) => {
      try {
        const url = new URL(value);
        // 只允许相对路径或同域名的 URL
        if (url.protocol && !['http:', 'https:'].includes(url.protocol)) {
          throw new Error('不支持的协议');
        }
        // 在这里可以添加更多的域名白名单检查
        return true;
      } catch (error) {
        throw new Error('无效的重定向URL');
      }
    }),

  // 通用文本输入验证（防止 XSS）
  safeText: (fieldName) => body(fieldName)
    .trim()
    .escape()
    .isLength({ max: 1000 })
    .withMessage(`${fieldName}长度不能超过1000个字符`),
};

/**
 * 常用验证组合
 */
const validationSets = {
  // 用户注册验证
  userRegistration: [
    validationRules.username,
    validationRules.email,
    validationRules.password,
    handleValidationErrors
  ],

  // 用户登录验证
  userLogin: [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty().withMessage('密码不能为空'),
    handleValidationErrors
  ],

  // ID 参数验证
  idParam: [
    validationRules.id,
    handleValidationErrors
  ]
};

module.exports = {
  validationRules,
  validationSets,
  handleValidationErrors
};
