var express = require('express');
var router = express.Router();
const { validationSets } = require('../utils/validation');

/* GET users listing. */
router.get('/', function(req, res, next) {
  res.json({
    message: 'Users API',
    version: '1.0.0'
  });
});

/* GET user by ID - 带输入验证 */
router.get('/:id', validationSets.idParam, function(req, res, next) {
  const userId = req.params.id;
  // 这里应该从数据库获取用户信息
  res.json({
    message: `获取用户 ${userId} 的信息`,
    userId: userId
  });
});

/* POST create user - 带输入验证 */
router.post('/', validationSets.userRegistration, function(req, res, next) {
  const { username, email } = req.body;
  // 这里应该创建用户到数据库
  res.status(201).json({
    message: '用户创建成功',
    user: {
      username,
      email
    }
  });
});

module.exports = router;
