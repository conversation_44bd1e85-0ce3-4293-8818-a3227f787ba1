# 安全配置说明

本项目已实施以下安全措施，基于 Express.js 官方安全最佳实践。

## 已实施的安全措施

### 1. HTTP 安全头 (Helmet)
- **Content-Security-Policy**: 防止 XSS 攻击
- **X-Frame-Options**: 防止点击劫持
- **X-Content-Type-Options**: 防止 MIME 类型嗅探
- **Strict-Transport-Security**: 强制使用 HTTPS
- **X-Powered-By**: 已禁用，减少指纹识别

### 2. 速率限制
- **全局限制**: 每个 IP 15分钟内最多 100 个请求
- **登录限制**: 每个 IP 15分钟内最多 5 次登录尝试
- 防止暴力攻击和 DDoS 攻击

### 3. 输入验证
- 使用 express-validator 进行输入验证
- 防止 SQL 注入和 XSS 攻击
- 验证邮箱、密码、用户名等关键字段

### 4. Cookie 安全
- 设置 `httpOnly` 防止 XSS 访问
- 生产环境启用 `secure` 标志
- 使用 `sameSite: 'strict'` 防止 CSRF

### 5. CORS 配置
- 开发环境允许所有源
- 生产环境需要配置具体的允许域名

### 6. 错误处理
- 生产环境不暴露敏感错误信息
- 记录详细的错误日志用于调试
- 自定义错误页面

### 7. 请求体大小限制
- JSON 和 URL 编码请求限制为 10MB
- 防止大文件攻击

## 环境配置

### 开发环境
1. 复制 `.env.example` 为 `.env`
2. 设置 `NODE_ENV=development`
3. 配置其他必要的环境变量

### 生产环境
1. 设置 `NODE_ENV=production`
2. 配置强密码作为 `COOKIE_SECRET` 和 `SESSION_SECRET`
3. 在 CORS 配置中指定具体的允许域名
4. 启用 HTTPS
5. 使用专业的日志系统

## 安全检查清单

- [ ] 定期更新依赖包 (`npm audit` 和 `npm update`)
- [ ] 使用强密码和密钥
- [ ] 配置防火墙和网络安全
- [ ] 启用 HTTPS/TLS
- [ ] 定期备份数据
- [ ] 监控异常访问和错误日志
- [ ] 实施访问控制和权限管理

## 推荐的额外安全措施

1. **会话管理**: 使用 `express-session` 配置安全的会话
2. **JWT 认证**: 实施 JSON Web Token 认证
3. **数据库安全**: 使用参数化查询防止 SQL 注入
4. **文件上传**: 如需文件上传，使用 `multer` 并验证文件类型
5. **日志记录**: 使用 `winston` 等专业日志库
6. **监控**: 实施应用性能监控 (APM)

## 安全漏洞报告

如发现安全漏洞，请通过以下方式报告：
- 邮箱: <EMAIL>
- 不要在公开的 issue 中报告安全漏洞

## 参考资源

- [Express.js 安全最佳实践](https://expressjs.com/en/advanced/best-practice-security.html)
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Node.js 安全检查清单](https://blog.risingstack.com/node-js-security-checklist/)
